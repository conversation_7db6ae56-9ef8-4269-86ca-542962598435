import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DocumentService } from '../../services/document.service';
import { AuthService } from '../../services/auth.service';
import { Document, TypeDocument, StatutDocument, TypeDemandeDocument } from '../models';

@Component({
  selector: 'app-admin-documents',
  templateUrl: './admin-documents.component.html',
  styleUrls: ['./admin-documents.component.css']
})
export class AdminDocumentsComponent implements OnInit {
  documents: Document[] = [];
  filteredDocuments: Document[] = [];
  typesDocuments: TypeDocument[] = [];
  loading = false;
  searchTerm = '';
  selectedStatut: StatutDocument | null = null;
  selectedType: string | null = null;

  // Formulaire pour créer un type de document
  typeDocumentForm!: FormGroup;
  showTypeForm = false;

  // Énumérations pour le template
  StatutDocument = StatutDocument;
  TypeDemandeDocument = TypeDemandeDocument;

  statutOptions = [
    { value: StatutDocument.EnAttente, label: 'En attente' },
    { value: StatutDocument.Approuve, label: 'Approuvé' },
    { value: StatutDocument.Rejete, label: 'Rejeté' }
  ];

  typeDemandeOptions = [
    { value: TypeDemandeDocument.AttestationTravail, label: 'Attestation de travail' },
    { value: TypeDemandeDocument.AttestationStage, label: 'Attestation de stage' },
    { value: TypeDemandeDocument.CertificatTravail, label: 'Certificat de travail' },
    { value: TypeDemandeDocument.Autre, label: 'Autre' }
  ];

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['titre', 'typeDemande', 'typeDocument', 'utilisateurDemandeur', 'dateCreation', 'statut', 'actions'];

  constructor(
    private documentService: DocumentService,
    public authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions - seuls les admins peuvent gérer les documents
    if (!this.authService.canManageDocuments()) {
      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeTypeForm();
    this.loadTypesDocuments();
    this.loadDocuments();
  }

  initializeTypeForm(): void {
    this.typeDocumentForm = this.formBuilder.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      description: ['']
    });
  }

  loadTypesDocuments(): void {
    this.documentService.getTypesDocuments().subscribe({
      next: (types: TypeDocument[]) => {
        this.typesDocuments = types;
      },
      error: (error: any) => {
        this.showError('Erreur lors du chargement des types de documents');
      }
    });
  }

  loadDocuments(): void {
    this.loading = true;
    this.documentService.getDocuments().subscribe({
      next: (documents) => {
        this.documents = documents;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des documents');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredDocuments = this.documents.filter(document => {
      const matchesSearch = !this.searchTerm ||
        document.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (document.utilisateurDemandeur?.nom.toLowerCase().includes(this.searchTerm.toLowerCase()));

      const matchesStatut = this.selectedStatut === null || document.statut === this.selectedStatut;
      const matchesType = this.selectedType === null || document.typeDocumentId === this.selectedType;

      return matchesSearch && matchesStatut && matchesType;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatutFilterChange(): void {
    this.applyFilters();
  }

  onTypeFilterChange(): void {
    this.applyFilters();
  }

  // Gestion des types de documents
  showTypeDocumentForm(): void {
    this.typeDocumentForm.reset();
    this.showTypeForm = true;
  }

  cancelTypeForm(): void {
    this.showTypeForm = false;
    this.typeDocumentForm.reset();
  }

  onSubmitType(): void {
    if (this.typeDocumentForm.invalid) {
      this.markFormGroupTouched(this.typeDocumentForm);
      return;
    }

    const typeData = this.typeDocumentForm.value;
    this.documentService.createTypeDocument(typeData).subscribe({
      next: (type) => {
        this.typesDocuments.push(type);
        this.showSuccess('Type de document créé avec succès');
        this.cancelTypeForm();
      },
      error: (error) => {
        this.showError('Erreur lors de la création du type de document');
      }
    });
  }

  deleteTypeDocument(type: TypeDocument): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le type "${type.nom}" ?`)) {
      this.documentService.deleteTypeDocument(type.id).subscribe({
        next: () => {
          this.typesDocuments = this.typesDocuments.filter(t => t.id !== type.id);
          this.showSuccess('Type de document supprimé avec succès');
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression du type de document');
        }
      });
    }
  }

  // Gestion des documents
  approveDocument(document: Document): void {
    this.changeDocumentStatus(document, StatutDocument.Approuve, 'approuver');
  }

  rejectDocument(document: Document): void {
    this.changeDocumentStatus(document, StatutDocument.Rejete, 'rejeter');
  }

  changeDocumentStatus(document: Document, newStatut: StatutDocument, action: string): void {
    if (confirm(`Êtes-vous sûr de vouloir ${action} le document "${document.titre}" ?`)) {
      this.loading = true;
      this.documentService.updateDocumentStatus(document.id, newStatut).subscribe({
        next: (updatedDocument) => {
          const index = this.documents.findIndex(d => d.id === document.id);
          if (index !== -1) {
            this.documents[index] = updatedDocument;
            this.applyFilters();
          }
          this.showSuccess(`Document ${action === 'approuver' ? 'approuvé' : 'rejeté'} avec succès`);
          this.loading = false;

          // Notification automatique à l'utilisateur (géré côté backend)
        },
        error: (error) => {
          this.showError(`Erreur lors de la modification du statut`);
          this.loading = false;
        }
      });
    }
  }

  deleteDocument(document: Document): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le document "${document.titre}" ?`)) {
      this.loading = true;
      this.documentService.deleteDocument(document.id).subscribe({
        next: () => {
          this.documents = this.documents.filter(d => d.id !== document.id);
          this.applyFilters();
          this.showSuccess('Document supprimé avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression du document');
          this.loading = false;
        }
      });
    }
  }

  getStatutLabel(statut: StatutDocument): string {
    const option = this.statutOptions.find(opt => opt.value === statut);
    return option ? option.label : 'Inconnu';
  }

  getStatutClass(statut: StatutDocument): string {
    switch (statut) {
      case StatutDocument.EnAttente:
        return 'statut-attente';
      case StatutDocument.Approuve:
        return 'statut-approuve';
      case StatutDocument.Rejete:
        return 'statut-rejete';
      default:
        return '';
    }
  }

  getTypeDemandeLabel(typeDemande: TypeDemandeDocument): string {
    const option = this.typeDemandeOptions.find(opt => opt.value === typeDemande);
    return option ? option.label : 'Inconnu';
  }

  getTypeDocumentName(typeDocumentId: string): string {
    const type = this.typesDocuments.find(t => t.id === typeDocumentId);
    return type ? type.nom : 'Type inconnu';
  }

  canApprove(document: Document): boolean {
    return document.statut === StatutDocument.EnAttente;
  }

  canReject(document: Document): boolean {
    return document.statut === StatutDocument.EnAttente;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.typeDocumentForm.get('nom'); }
  get description() { return this.typeDocumentForm.get('description'); }
}
