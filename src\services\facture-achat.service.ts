import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FactureAchat } from 'src/app/models';

@Injectable({
  providedIn: 'root'
})
export class FactureAchatService {
  private readonly API_URL = '/api/factures-achat';

  constructor(private http: HttpClient) {}

  getFacturesAchat(): Observable<FactureAchat[]> {
    return this.http.get<FactureAchat[]>(this.API_URL);
  }

  getFactureAchat(id: string): Observable<FactureAchat> {
    return this.http.get<FactureAchat>(`${this.API_URL}/${id}`);
  }

  createFactureAchat(factureData: any): Observable<FactureAchat> {
    const formData = new FormData();
    
    // Ajouter les données de la facture
    formData.append('numero', factureData.numero);
    formData.append('date', factureData.date.toISOString());
    formData.append('montant', factureData.montant.toString());
    formData.append('fournisseurId', factureData.fournisseurId);
    
    if (factureData.dateEcheance) {
      formData.append('dateEcheance', factureData.dateEcheance.toISOString());
    }
    
    if (factureData.notesInternes) {
      formData.append('notesInternes', factureData.notesInternes);
    }
    
    if (factureData.statut !== undefined) {
      formData.append('statut', factureData.statut.toString());
    }
    
    // Ajouter le fichier si présent
    if (factureData.fichier) {
      formData.append('fichier', factureData.fichier);
    }
    
    return this.http.post<FactureAchat>(this.API_URL, formData);
  }

  updateFactureAchat(id: string, factureData: any): Observable<FactureAchat> {
    const formData = new FormData();
    
    formData.append('numero', factureData.numero);
    formData.append('date', factureData.date.toISOString());
    formData.append('montant', factureData.montant.toString());
    formData.append('fournisseurId', factureData.fournisseurId);
    formData.append('statut', factureData.statut.toString());
    
    if (factureData.dateEcheance) {
      formData.append('dateEcheance', factureData.dateEcheance.toISOString());
    }
    
    if (factureData.notesInternes) {
      formData.append('notesInternes', factureData.notesInternes);
    }
    
    if (factureData.fichier) {
      formData.append('fichier', factureData.fichier);
    }
    
    return this.http.put<FactureAchat>(`${this.API_URL}/${id}`, formData);
  }

  deleteFactureAchat(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  downloadFile(id: string): Observable<Blob> {
    return this.http.get(`${this.API_URL}/${id}/download`, { 
      responseType: 'blob' 
    });
  }

  uploadFile(id: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('fichier', file);
    return this.http.post(`${this.API_URL}/${id}/upload`, formData);
  }

  getFacturesByFournisseur(fournisseurId: string): Observable<FactureAchat[]> {
    const params = new HttpParams().set('fournisseurId', fournisseurId);
    return this.http.get<FactureAchat[]>(this.API_URL, { params });
  }

  getFacturesByStatut(statut: number): Observable<FactureAchat[]> {
    const params = new HttpParams().set('statut', statut.toString());
    return this.http.get<FactureAchat[]>(this.API_URL, { params });
  }

  searchFactures(searchTerm: string): Observable<FactureAchat[]> {
    const params = new HttpParams().set('search', searchTerm);
    return this.http.get<FactureAchat[]>(`${this.API_URL}/search`, { params });
  }

  // Méthodes pour l'upload de fichiers
  createFactureAchatWithFile(formData: FormData): Observable<FactureAchat> {
    return this.http.post<FactureAchat>(`${this.API_URL}/with-file`, formData);
  }

  updateFactureAchatWithFile(id: string, formData: FormData): Observable<FactureAchat> {
    return this.http.put<FactureAchat>(`${this.API_URL}/${id}/with-file`, formData);
  }



  deleteFile(factureId: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${factureId}/file`);
  }
}
