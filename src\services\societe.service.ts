import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Societe } from 'src/app/models';

@Injectable({
  providedIn: 'root'
})
export class SocieteService {
  private readonly API_URL = '/api/societes';

  constructor(private http: HttpClient) {}

  // Le backend n'a que GET et PUT pour la société de l'utilisateur connecté
  getSociete(): Observable<Societe> {
    return this.http.get<Societe>(this.API_URL);
  }

  updateSociete(societe: Partial<Societe>): Observable<void> {
    return this.http.put<void>(this.API_URL, societe);
  }

  // Ces endpoints n'existent pas dans le backend
  // Les fichiers sont gérés directement dans updateSociete
}
