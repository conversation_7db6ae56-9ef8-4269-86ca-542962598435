import { StatutDocument, TypeDemandeDocument } from './enums';
import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

export interface TypeDocument {
  id: string;
  nom: string;
  description?: string;
  societeId: string;
  societe?: Societe;
}

export interface Document {
  id: string;
  titre: string;
  contenu: string;
  dateCreation: Date;
  dateModification?: Date;
  statut: StatutDocument;
  typeDemande: TypeDemandeDocument;
  typeDocumentId: string;
  societeId: string;
  utilisateurDemandeurId?: string;
  utilisateurApprobateurId?: string;
  typeDocument?: TypeDocument;
  societe?: Societe;
  utilisateurDemandeur?: Applicationuser;
  utilisateurApprobateur?: Applicationuser;
}


