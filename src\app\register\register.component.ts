import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit {
  registerForm!: FormGroup;
  loading = false;
  error = '';
  hidePassword = true;
  hideConfirmPassword = true;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Rediriger si déjà connecté
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
      return;
    }

    this.registerForm = this.formBuilder.group({
      // Informations utilisateur
      nom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      userName: ['', [Validators.required, Validators.minLength(3)]],
      motDePasse: ['', [Validators.required, Validators.minLength(6)]],
      confirmMotDePasse: ['', [Validators.required]],

      // Informations société
      nomSociete: ['', [Validators.required, Validators.minLength(2)]],
      adresseSociete: ['', [Validators.required]],
      matriculeFiscale: ['', [Validators.required, Validators.minLength(8)]],
      emailSociete: ['', [Validators.email]],
      telephoneSociete: ['']
    }, { validators: this.passwordMatchValidator });
  }

  // Validateur personnalisé pour vérifier que les mots de passe correspondent
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('motDePasse');
    const confirmPassword = form.get('confirmMotDePasse');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  onSubmit(): void {
    if (this.registerForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    this.error = '';

    const formValue = this.registerForm.value;

    // Préparer les données pour l'inscription
    // Le premier utilisateur sera automatiquement Admin
    const registrationData = {
      // Données utilisateur
      nom: formValue.nom,
      email: formValue.email,
      userName: formValue.userName,
      motDePasse: formValue.motDePasse,

      // Données société (création automatique)
      societe: {
        nom: formValue.nomSociete,
        adresse: formValue.adresseSociete,
        matriculeFiscale: formValue.matriculeFiscale,
        email: formValue.emailSociete,
        telephone: formValue.telephoneSociete
      }
    };

    this.authService.register(registrationData).subscribe({
      next: () => {
        this.loading = false;
        // Redirection automatique après inscription réussie
        this.router.navigate(['/admin-dashboard']);
      },
      error: (error) => {
        this.loading = false;
        this.error = error.error?.message || 'Erreur lors de l\'inscription. Veuillez réessayer.';
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.registerForm.controls).forEach(key => {
      const control = this.registerForm.get(key);
      control?.markAsTouched();
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.registerForm.get('nom'); }
  get email() { return this.registerForm.get('email'); }
  get userName() { return this.registerForm.get('userName'); }
  get motDePasse() { return this.registerForm.get('motDePasse'); }
  get confirmMotDePasse() { return this.registerForm.get('confirmMotDePasse'); }
  get nomSociete() { return this.registerForm.get('nomSociete'); }
  get adresseSociete() { return this.registerForm.get('adresseSociete'); }
  get matriculeFiscale() { return this.registerForm.get('matriculeFiscale'); }
  get emailSociete() { return this.registerForm.get('emailSociete'); }
  get telephoneSociete() { return this.registerForm.get('telephoneSociete'); }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }
}
