import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly API_URL = 'http://localhost:5251/api/utilisateurs';

  constructor(private http: HttpClient) {}

  // Le backend retourne des DTOs, pas des entités directement
  getUtilisateurs(): Observable<any[]> {
    return this.http.get<any[]>(this.API_URL);
  }

  getUtilisateur(id: string): Observable<any> {
    return this.http.get<any>(`${this.API_URL}/${id}`);
  }

  // Le backend utilise des DTOs spécifiques
  createUtilisateur(userData: any): Observable<any> {
    return this.http.post<any>(this.API_URL, userData);
  }

  updateUtilisateur(id: string, userData: any): Observable<void> {
    return this.http.put<void>(`${this.API_URL}/${id}`, userData);
  }

  deleteUtilisateur(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  // Ces endpoints n'existent pas dans le backend actuel
  // Seuls les CRUD de base sont disponibles
}
