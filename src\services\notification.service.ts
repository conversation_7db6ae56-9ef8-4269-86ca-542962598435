import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { Notification } from '../app/models';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly API_URL = '/api/notifications';
  
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadUnreadCount();
  }

  getNotifications(): Observable<Notification[]> {
    return this.http.get<Notification[]>(this.API_URL);
  }

  getNotification(id: string): Observable<Notification> {
    return this.http.get<Notification>(`${this.API_URL}/${id}`);
  }

  getUnreadNotifications(): Observable<Notification[]> {
    return this.http.get<Notification[]>(`${this.API_URL}/unread`);
  }

  markAsRead(id: string): Observable<Notification> {
    return this.http.patch<Notification>(`${this.API_URL}/${id}/mark-read`, {})
      .pipe(
        tap(() => this.loadUnreadCount())
      );
  }

  markAllAsRead(): Observable<any> {
    return this.http.patch(`${this.API_URL}/mark-all-read`, {})
      .pipe(
        tap(() => this.loadUnreadCount())
      );
  }

  deleteNotification(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`)
      .pipe(
        tap(() => this.loadUnreadCount())
      );
  }

  getNotificationsByType(type: number): Observable<Notification[]> {
    const params = new HttpParams().set('type', type.toString());
    return this.http.get<Notification[]>(this.API_URL, { params });
  }

  getUnreadCount(): Observable<number> {
    // Le backend n'a pas cet endpoint, on compte les notifications non lues
    return this.getUnreadNotifications().pipe(
      map(notifications => notifications.length)
    );
  }

  private loadUnreadCount(): void {
    this.getUnreadCount().subscribe(count => {
      this.unreadCountSubject.next(count);
    });
  }

  // Méthodes pour créer des notifications (côté admin)
  createNotification(notification: Partial<Notification>): Observable<Notification> {
    return this.http.post<Notification>(this.API_URL, notification);
  }

  sendNotificationToAll(titre: string, message: string, type: number): Observable<any> {
    return this.http.post(`${this.API_URL}/send-to-all`, {
      titre,
      message,
      type
    });
  }

  sendNotificationToUser(utilisateurId: string, titre: string, message: string, type: number): Observable<any> {
    return this.http.post(`${this.API_URL}/send-to-user`, {
      utilisateurId,
      titre,
      message,
      type
    });
  }
}
