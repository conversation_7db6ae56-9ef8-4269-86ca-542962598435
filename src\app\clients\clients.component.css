.clients-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h1 {
  display: flex;
  align-items: center;
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.header h1 mat-icon {
  margin-right: 12px;
  color: #667eea;
}

.filters-card {
  margin-bottom: 24px;
}

.filters-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-field {
  flex: 2;
}

.filter-field {
  flex: 1;
}

.form-card {
  margin-bottom: 24px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.table-card {
  margin-bottom: 24px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px;
  color: #666;
}

.no-data mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #ccc;
}

.no-data p {
  margin-bottom: 24px;
  font-size: 16px;
}

.clients-table {
  width: 100%;
}

.client-info {
  display: flex;
  align-items: center;
}

.client-icon {
  margin-right: 8px;
  color: #667eea;
}

.client-name {
  font-weight: 500;
}

.email-link {
  color: #667eea;
  text-decoration: none;
}

.email-link:hover {
  text-decoration: underline;
}

.no-data-text {
  color: #999;
  font-style: italic;
}

.statut-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.statut-actif {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.statut-inactif {
  background-color: #fff3e0;
  color: #f57c00;
}

.statut-suspendu {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Responsive design */
@media (max-width: 768px) {
  .clients-container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
    gap: 12px;
  }

  .search-field,
  .filter-field {
    flex: none;
    width: 100%;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .half-width {
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .clients-table {
    font-size: 14px;
  }
}