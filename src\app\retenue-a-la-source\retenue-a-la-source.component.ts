import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { RetenueSourceService } from '../../services/retenue-source.service';
import { AuthService } from '../../services/auth.service';
import { RetenueSource } from '../models';

@Component({
  selector: 'app-retenue-a-la-source',
  templateUrl: './retenue-a-la-source.component.html',
  styleUrls: ['./retenue-a-la-source.component.css']
})
export class RetenueALaSourceComponent implements OnInit {
  retenues: RetenueSource[] = [];
  loading = false;

  // Gestion des fichiers
  selectedFile: File | null = null;
  dragOver = false;
  uploading = false;

  // Types de fichiers acceptés pour les retenues
  acceptedFileTypes = ['.pdf', '.jpg', '.jpeg', '.png'];
  maxFileSize = 5 * 1024 * 1024; // 5MB

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['dateScan', 'nomFichier', 'tailleFichier', 'actions'];

  constructor(
    private retenueSourceService: RetenueSourceService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadRetenues();
  }

  loadRetenues(): void {
    this.loading = true;
    this.retenueSourceService.getRetenuesSources().subscribe({
      next: (retenues) => {
        // Les utilisateurs ne voient que leurs propres retenues
        // Les admins voient toutes les retenues de leur société
        this.retenues = retenues;
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des retenues');
        this.loading = false;
      }
    });
  }

  // Gestion des fichiers
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  onFileDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
  }

  handleFileSelection(file: File): void {
    // Vérifier le type de fichier
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!this.acceptedFileTypes.includes(fileExtension)) {
      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedFileTypes.join(', '));
      return;
    }

    // Vérifier la taille du fichier
    if (file.size > this.maxFileSize) {
      this.showError('Fichier trop volumineux. Taille maximale: 5MB');
      return;
    }

    this.selectedFile = file;
  }

  uploadFile(): void {
    if (!this.selectedFile) {
      this.showError('Veuillez sélectionner un fichier');
      return;
    }

    this.uploading = true;
    const formData = new FormData();
    formData.append('fichier', this.selectedFile);

    this.retenueSourceService.uploadRetenue(formData).subscribe({
      next: (retenue) => {
        this.retenues.unshift(retenue); // Ajouter en début de liste
        this.showSuccess('Retenue à la source ajoutée avec succès');
        this.resetFileSelection();
        this.uploading = false;

        // Notification automatique aux admins (géré côté backend)
      },
      error: (error) => {
        this.showError('Erreur lors de l\'upload de la retenue');
        this.uploading = false;
      }
    });
  }

  removeFile(): void {
    this.resetFileSelection();
  }

  resetFileSelection(): void {
    this.selectedFile = null;
    this.dragOver = false;
  }

  downloadFile(retenue: RetenueSource): void {
    if (retenue.cheminFichier) {
      this.retenueSourceService.downloadFile(retenue.id).subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `retenue_${retenue.dateScan ? new Date(retenue.dateScan).toLocaleDateString() : 'document'}.pdf`;
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          this.showError('Erreur lors du téléchargement du fichier');
        }
      });
    }
  }

  deleteRetenue(retenue: RetenueSource): void {
    // Seuls les admins ou le propriétaire peuvent supprimer
    const currentUser = this.authService.getCurrentUser();
    if (!this.authService.isAdmin() && currentUser?.id !== retenue.utilisateurId) {
      this.showError('Vous ne pouvez supprimer que vos propres retenues');
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer cette retenue à la source ?')) {
      this.loading = true;
      this.retenueSourceService.deleteRetenue(retenue.id).subscribe({
        next: () => {
          this.retenues = this.retenues.filter(r => r.id !== retenue.id);
          this.showSuccess('Retenue supprimée avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression de la retenue');
          this.loading = false;
        }
      });
    }
  }

  getFileSize(sizeInBytes: number | undefined): string {
    if (!sizeInBytes) return '';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = sizeInBytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  canDelete(retenue: RetenueSource): boolean {
    const currentUser = this.authService.getCurrentUser();
    return this.authService.isAdmin() || (currentUser?.id === retenue.utilisateurId);
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
