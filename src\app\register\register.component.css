.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 600px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.half-width {
  flex: 1;
}

.section-title {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 18px;
  font-weight: 500;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.section-title mat-icon {
  margin-right: 8px;
  color: #667eea;
}

.register-button {
  height: 48px;
  font-size: 16px;
  margin-top: 24px;
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #ffebee;
  border-radius: 8px;
  border-left: 4px solid #f44336;
}

.error-message mat-icon {
  margin-right: 8px;
}

.login-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.login-link:hover {
  text-decoration: underline;
}

mat-card-header {
  text-align: center;
  margin-bottom: 24px;
}

mat-card-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

mat-card-subtitle {
  color: #666;
  margin-top: 8px;
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .half-width {
    width: 100%;
  }

  .register-card {
    margin: 10px;
    padding: 16px;
  }
}