import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

// Angular Material Modules
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LoginComponent } from './login/login.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { RegisterComponent } from './register/register.component';
import { UsersidebarComponent } from './usersidebar/usersidebar.component';
import { AdminsidebarComponent } from './adminsidebar/adminsidebar.component';
import { AdmindashboardComponent } from './admindashboard/admindashboard.component';
import { UsersListComponent } from './users-list/users-list.component';
import { ClientsComponent } from './clients/clients.component';
import { FournisseursComponent } from './fournisseurs/fournisseurs.component';
import { RetenueALaSourceComponent } from './retenue-a-la-source/retenue-a-la-source.component';
import { UserProfileComponent } from './user-profile/user-profile.component';
import { ParametresSocietesComponent } from './parametres-societes/parametres-societes.component';
import { FacturesAchatComponent } from './factures-achat/factures-achat.component';
import { FacturesVenteComponent } from './factures-vente/factures-vente.component';
import { AdminDocumentsComponent } from './admin-documents/admin-documents.component';
import { UserDocumentsComponent } from './user-documents/user-documents.component';

@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    DashboardComponent,
    RegisterComponent,
    UsersidebarComponent,
    AdminsidebarComponent,
    AdmindashboardComponent,
    UsersListComponent,
    ClientsComponent,
    FournisseursComponent,
    RetenueALaSourceComponent,
    UserProfileComponent,
    ParametresSocietesComponent,
    FacturesAchatComponent,
    FacturesVenteComponent,
    AdminDocumentsComponent,
    UserDocumentsComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    AppRoutingModule,
    // Angular Material Modules
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatCardModule,
    MatTableModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
