import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SocieteService } from '../../services/societe.service';
import { AuthService } from '../../services/auth.service';
import { Societe } from '../models';

@Component({
  selector: 'app-parametres-societes',
  templateUrl: './parametres-societes.component.html',
  styleUrls: ['./parametres-societes.component.css']
})
export class ParametresSocietesComponent implements OnInit {
  societeForm!: FormGroup;
  societe: Societe | null = null;
  loading = false;
  saving = false;

  // Gestion des fichiers
  logoFile: File | null = null;
  signatureFile: File | null = null;
  cachetFile: File | null = null;

  logoPreview: string | null = null;
  signaturePreview: string | null = null;
  cachetPreview: string | null = null;

  // Types de fichiers acceptés pour les images
  acceptedImageTypes = ['.jpg', '.jpeg', '.png', '.gif'];
  maxImageSize = 2 * 1024 * 1024; // 2MB

  constructor(
    private societeService: SocieteService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions - seuls les admins peuvent modifier la société
    if (!this.authService.canManageCompany()) {
      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeForm();
    this.loadSociete();
  }

  initializeForm(): void {
    this.societeForm = this.formBuilder.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      adresse: ['', [Validators.required]],
      matriculeFiscale: ['', [Validators.required, Validators.minLength(8)]],
      email: ['', [Validators.email]],
      telephone: ['']
    });
  }

  loadSociete(): void {
    this.loading = true;
    this.societeService.getSociete().subscribe({
      next: (societe) => {
        this.societe = societe;
        this.societeForm.patchValue({
          nom: societe.nom,
          adresse: societe.adresse,
          matriculeFiscale: societe.matriculeFiscale,
          email: societe.email || '',
          telephone: societe.telephone || ''
        });

        // Charger les aperçus des images existantes
        if (societe.logo) {
          this.logoPreview = societe.logo;
        }
        if (societe.signature) {
          this.signaturePreview = societe.signature;
        }
        if (societe.cachet) {
          this.cachetPreview = societe.cachet;
        }

        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des informations de la société');
        this.loading = false;
      }
    });
  }

  // Gestion des fichiers
  onLogoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleImageSelection(file, 'logo');
    }
  }

  onSignatureSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleImageSelection(file, 'signature');
    }
  }

  onCachetSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleImageSelection(file, 'cachet');
    }
  }

  handleImageSelection(file: File, type: 'logo' | 'signature' | 'cachet'): void {
    // Vérifier le type de fichier
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!this.acceptedImageTypes.includes(fileExtension)) {
      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedImageTypes.join(', '));
      return;
    }

    // Vérifier la taille du fichier
    if (file.size > this.maxImageSize) {
      this.showError('Fichier trop volumineux. Taille maximale: 2MB');
      return;
    }

    // Stocker le fichier selon le type
    switch (type) {
      case 'logo':
        this.logoFile = file;
        break;
      case 'signature':
        this.signatureFile = file;
        break;
      case 'cachet':
        this.cachetFile = file;
        break;
    }

    // Créer un aperçu
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      switch (type) {
        case 'logo':
          this.logoPreview = result;
          break;
        case 'signature':
          this.signaturePreview = result;
          break;
        case 'cachet':
          this.cachetPreview = result;
          break;
      }
    };
    reader.readAsDataURL(file);
  }

  removeLogo(): void {
    this.logoFile = null;
    this.logoPreview = null;
  }

  removeSignature(): void {
    this.signatureFile = null;
    this.signaturePreview = null;
  }

  removeCachet(): void {
    this.cachetFile = null;
    this.cachetPreview = null;
  }

  onSubmit(): void {
    if (this.societeForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.saving = true;
    const formData = this.societeForm.value;

    // Préparer les données avec les fichiers
    const updateData: any = { ...formData };

    // Convertir les fichiers en base64 si nécessaire
    const promises: Promise<void>[] = [];

    if (this.logoFile) {
      promises.push(this.convertFileToBase64(this.logoFile).then(base64 => {
        updateData.logo = base64;
      }));
    }

    if (this.signatureFile) {
      promises.push(this.convertFileToBase64(this.signatureFile).then(base64 => {
        updateData.signature = base64;
      }));
    }

    if (this.cachetFile) {
      promises.push(this.convertFileToBase64(this.cachetFile).then(base64 => {
        updateData.cachet = base64;
      }));
    }

    Promise.all(promises).then(() => {
      this.societeService.updateSociete(updateData).subscribe({
        next: () => {
          this.showSuccess('Informations de la société mises à jour avec succès');
          this.loadSociete(); // Recharger pour obtenir les données mises à jour
          this.saving = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la mise à jour des informations');
          this.saving = false;
        }
      });
    });
  }

  private convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.societeForm.controls).forEach(key => {
      const control = this.societeForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.societeForm.get('nom'); }
  get adresse() { return this.societeForm.get('adresse'); }
  get matriculeFiscale() { return this.societeForm.get('matriculeFiscale'); }
  get email() { return this.societeForm.get('email'); }
  get telephone() { return this.societeForm.get('telephone'); }
}
