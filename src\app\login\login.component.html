<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title>Connexion</mat-card-title>
      <mat-card-subtitle>Connectez-vous à votre compte</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <!-- Champ Email -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="email?.hasError('required')">
            L'email est requis
          </mat-error>
          <mat-error *ngIf="email?.hasError('email')">
            Format d'email invalide
          </mat-error>
        </mat-form-field>

        <!-- Champ Mot de passe -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Mot de passe</mat-label>
          <input matInput [type]="hidePassword ? 'password' : 'text'"
                 formControlName="motDePasse" placeholder="Votre mot de passe">
          <button mat-icon-button matSuffix (click)="togglePasswordVisibility()"
                  type="button" [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hidePassword">
            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="motDePasse?.hasError('required')">
            Le mot de passe est requis
          </mat-error>
          <mat-error *ngIf="motDePasse?.hasError('minlength')">
            Le mot de passe doit contenir au moins 6 caractères
          </mat-error>
        </mat-form-field>

        <!-- Message d'erreur -->
        <div *ngIf="error" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ error }}</span>
        </div>

        <!-- Bouton de connexion -->
        <button mat-raised-button color="primary" type="submit"
                class="full-width login-button" [disabled]="loading">
          <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
          <span *ngIf="!loading">Se connecter</span>
          <span *ngIf="loading">Connexion en cours...</span>
        </button>
      </form>
    </mat-card-content>

    <mat-card-actions>
      <p>Pas encore de compte ?
        <a routerLink="/register" class="register-link">S'inscrire</a>
      </p>
    </mat-card-actions>
  </mat-card>
</div>
