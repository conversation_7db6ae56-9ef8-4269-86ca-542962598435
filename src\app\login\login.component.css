.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.login-button {
  height: 48px;
  font-size: 16px;
  margin-top: 16px;
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 4px solid #f44336;
}

.error-message mat-icon {
  margin-right: 8px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.register-link:hover {
  text-decoration: underline;
}

mat-card-header {
  text-align: center;
  margin-bottom: 24px;
}

mat-card-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

mat-card-subtitle {
  color: #666;
  margin-top: 8px;
}