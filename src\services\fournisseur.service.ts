import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Fournisseur } from '../models';

@Injectable({
  providedIn: 'root'
})
export class FournisseurService {
  private readonly API_URL = '/api/fournisseurs';

  constructor(private http: HttpClient) {}

  getFournisseurs(): Observable<Fournisseur[]> {
    return this.http.get<Fournisseur[]>(this.API_URL);
  }

  getFournisseur(id: string): Observable<Fournisseur> {
    return this.http.get<Fournisseur>(`${this.API_URL}/${id}`);
  }

  createFournisseur(fournisseur: Partial<Fournisseur>): Observable<Fournisseur> {
    return this.http.post<Fournisseur>(this.API_URL, fournisseur);
  }

  updateFournisseur(id: string, fournisseur: Partial<Fournisseur>): Observable<Fournisseur> {
    return this.http.put<Fournisseur>(`${this.API_URL}/${id}`, fournisseur);
  }

  deleteFournisseur(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  // Ces endpoints n'existent pas dans le backend
  // Seuls les CRUD de base sont disponibles
}
