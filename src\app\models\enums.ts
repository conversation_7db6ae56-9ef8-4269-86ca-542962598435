export enum StatutClient {
  Actif = 0,
  Inactif = 1,
  Suspendu = 2
}

export enum StatutFournisseur {
  Actif = 0,
  Inactif = 1,
  Suspendu = 2
}

export enum StatutFacture {
  Brouillon = 0,
  Envoyee = 1,
  Payee = 2,
  EnRetard = 3,
  Annulee = 4
}

export enum StatutDocument {
  EnAttente = 0,
  Approuve = 1,
  Rejete = 2
}

export enum TypeDemandeDocument {
  AttestationTravail = 0,
  AttestationStage = 1,
  CertificatTravail = 2,
  Autre = 3
}

export enum TypeNotification {
  FactureCree = 0,
  FactureStatutChange = 1,
  DocumentStatutChange = 2,
  DocumentDemande = 3,
  UtilisateurCree = 4,
  RetenueSourceAjoutee = 5,
  Systeme = 6
}

export enum StatutNotification {
  NonLue = 0,
  Lue = 1
}
