import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FactureVenteService } from '../../services/facture-vente.service';
import { ClientService } from '../../services/client.service';
import { AuthService } from '../../services/auth.service';
import { FactureVente, StatutFacture, Client } from '../models';

@Component({
  selector: 'app-factures-vente',
  templateUrl: './factures-vente.component.html',
  styleUrls: ['./factures-vente.component.css']
})
export class FacturesVenteComponent implements OnInit {
  factures: FactureVente[] = [];
  filteredFactures: FactureVente[] = [];
  clients: Client[] = [];
  loading = false;
  searchTerm = '';
  selectedStatut: StatutFacture | null = null;
  selectedClient: string | null = null;

  // Formulaire pour ajouter/modifier une facture
  factureForm!: FormGroup;
  isEditing = false;
  editingFactureId: string | null = null;
  showForm = false;

  // Énumérations pour le template
  StatutFacture = StatutFacture;
  statutOptions = [
    { value: StatutFacture.Brouillon, label: 'Brouillon' },
    { value: StatutFacture.Envoyee, label: 'Envoyée' },
    { value: StatutFacture.Payee, label: 'Payée' },
    { value: StatutFacture.EnRetard, label: 'En retard' },
    { value: StatutFacture.Annulee, label: 'Annulée' }
  ];

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['numero', 'client', 'montant', 'date', 'dateEcheance', 'statut', 'actions'];

  constructor(
    private factureVenteService: FactureVenteService,
    private clientService: ClientService,
    public authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions
    if (!this.authService.canCreateInvoices()) {
      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeForm();
    this.loadClients();
    this.loadFactures();
  }

  initializeForm(): void {
    this.factureForm = this.formBuilder.group({
      numero: ['', [Validators.required]],
      clientId: ['', [Validators.required]],
      montant: ['', [Validators.required, Validators.min(0.01)]],
      date: [new Date(), [Validators.required]],
      dateEcheance: [''],
      statut: [StatutFacture.Brouillon, [Validators.required]],
      notesInternes: ['']
    });
  }

  loadClients(): void {
    this.clientService.getClients().subscribe({
      next: (clients) => {
        this.clients = clients.filter(c => c.statut === 0); // Seulement les clients actifs
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des clients');
      }
    });
  }

  loadFactures(): void {
    this.loading = true;
    this.factureVenteService.getFacturesVente().subscribe({
      next: (factures) => {
        this.factures = factures;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des factures');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredFactures = this.factures.filter(facture => {
      const matchesSearch = !this.searchTerm ||
        facture.numero.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (facture.client?.nom.toLowerCase().includes(this.searchTerm.toLowerCase()));

      const matchesStatut = this.selectedStatut === null || facture.statut === this.selectedStatut;
      const matchesClient = this.selectedClient === null || facture.clientId === this.selectedClient;

      return matchesSearch && matchesStatut && matchesClient;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatutFilterChange(): void {
    this.applyFilters();
  }

  onClientFilterChange(): void {
    this.applyFilters();
  }

  showAddForm(): void {
    this.isEditing = false;
    this.editingFactureId = null;
    this.factureForm.reset();
    this.factureForm.patchValue({
      date: new Date(),
      statut: StatutFacture.Brouillon
    });
    this.generateFactureNumber();
    this.showForm = true;
  }

  editFacture(facture: FactureVente): void {
    this.isEditing = true;
    this.editingFactureId = facture.id;
    this.factureForm.patchValue({
      numero: facture.numero,
      clientId: facture.clientId,
      montant: facture.montant,
      date: new Date(facture.date),
      dateEcheance: facture.dateEcheance ? new Date(facture.dateEcheance) : null,
      statut: facture.statut,
      notesInternes: facture.notesInternes
    });
    this.showForm = true;
  }

  cancelForm(): void {
    this.showForm = false;
    this.isEditing = false;
    this.editingFactureId = null;
    this.factureForm.reset();
  }

  onSubmit(): void {
    if (this.factureForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const factureData = this.factureForm.value;

    if (this.isEditing && this.editingFactureId) {
      this.updateFacture(this.editingFactureId, factureData);
    } else {
      this.createFacture(factureData);
    }
  }

  createFacture(factureData: any): void {
    this.loading = true;
    this.factureVenteService.createFactureVente(factureData).subscribe({
      next: (facture) => {
        this.factures.push(facture);
        this.applyFilters();
        this.showSuccess('Facture créée avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la création de la facture');
        this.loading = false;
      }
    });
  }

  updateFacture(id: string, factureData: any): void {
    this.loading = true;
    this.factureVenteService.updateFactureVente(id, factureData).subscribe({
      next: (updatedFacture) => {
        const index = this.factures.findIndex(f => f.id === id);
        if (index !== -1) {
          this.factures[index] = updatedFacture;
          this.applyFilters();
        }
        this.showSuccess('Facture modifiée avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la modification de la facture');
        this.loading = false;
      }
    });
  }

  deleteFacture(facture: FactureVente): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la facture "${facture.numero}" ?`)) {
      this.loading = true;
      this.factureVenteService.deleteFactureVente(facture.id).subscribe({
        next: () => {
          this.factures = this.factures.filter(f => f.id !== facture.id);
          this.applyFilters();
          this.showSuccess('Facture supprimée avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression de la facture');
          this.loading = false;
        }
      });
    }
  }

  changeStatut(facture: FactureVente, newStatut: StatutFacture): void {
    const statutLabel = this.getStatutLabel(newStatut);
    if (confirm(`Changer le statut de la facture "${facture.numero}" vers "${statutLabel}" ?`)) {
      this.loading = true;
      this.factureVenteService.updateFactureVente(facture.id, { statut: newStatut }).subscribe({
        next: (updatedFacture) => {
          const index = this.factures.findIndex(f => f.id === facture.id);
          if (index !== -1) {
            this.factures[index] = updatedFacture;
            this.applyFilters();
          }
          this.showSuccess(`Statut changé vers "${statutLabel}"`);
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors du changement de statut');
          this.loading = false;
        }
      });
    }
  }

  generateFactureNumber(): void {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');

    const numero = `FV-${year}${month}${day}-${time}`;
    this.factureForm.patchValue({ numero });
  }

  getStatutLabel(statut: StatutFacture): string {
    const option = this.statutOptions.find(opt => opt.value === statut);
    return option ? option.label : 'Inconnu';
  }

  getStatutClass(statut: StatutFacture): string {
    switch (statut) {
      case StatutFacture.Brouillon:
        return 'statut-brouillon';
      case StatutFacture.Envoyee:
        return 'statut-envoyee';
      case StatutFacture.Payee:
        return 'statut-payee';
      case StatutFacture.EnRetard:
        return 'statut-retard';
      case StatutFacture.Annulee:
        return 'statut-annulee';
      default:
        return '';
    }
  }

  getClientName(clientId: string): string {
    const client = this.clients.find(c => c.id === clientId);
    return client ? client.nom : 'Client inconnu';
  }

  isOverdue(facture: FactureVente): boolean {
    if (!facture.dateEcheance || facture.statut === StatutFacture.Payee) {
      return false;
    }
    return new Date(facture.dateEcheance) < new Date();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.factureForm.controls).forEach(key => {
      const control = this.factureForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get numero() { return this.factureForm.get('numero'); }
  get clientId() { return this.factureForm.get('clientId'); }
  get montant() { return this.factureForm.get('montant'); }
  get date() { return this.factureForm.get('date'); }
  get dateEcheance() { return this.factureForm.get('dateEcheance'); }
  get statut() { return this.factureForm.get('statut'); }
  get notesInternes() { return this.factureForm.get('notesInternes'); }
}
