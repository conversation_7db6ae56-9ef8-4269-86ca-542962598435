import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Document, TypeDocument } from 'src/app/models';

@Injectable({
  providedIn: 'root'
})
export class DocumentService {
  private readonly API_URL = 'http://localhost:5251/api/documents';
  private readonly TYPE_DOCUMENT_URL = 'http://localhost:5251/api/type-documents';

  constructor(private http: HttpClient) {}

  // Documents
  getDocuments(): Observable<Document[]> {
    return this.http.get<Document[]>(this.API_URL);
  }

  getDocument(id: string): Observable<Document> {
    return this.http.get<Document>(`${this.API_URL}/${id}`);
  }

  createDocument(document: Partial<Document>): Observable<Document> {
    return this.http.post<Document>(this.API_URL, document);
  }

  updateDocument(id: string, document: Partial<Document>): Observable<Document> {
    return this.http.put<Document>(`${this.API_URL}/${id}`, document);
  }

  deleteDocument(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  approveDocument(id: string): Observable<Document> {
    return this.http.patch<Document>(`${this.API_URL}/${id}/approve`, {});
  }

  rejectDocument(id: string, commentaire?: string): Observable<Document> {
    return this.http.patch<Document>(`${this.API_URL}/${id}/reject`, { commentaire });
  }

  getDocumentsByStatut(statut: number): Observable<Document[]> {
    const params = new HttpParams().set('statut', statut.toString());
    return this.http.get<Document[]>(this.API_URL, { params });
  }

  getDocumentsByType(typeId: string): Observable<Document[]> {
    const params = new HttpParams().set('typeId', typeId);
    return this.http.get<Document[]>(this.API_URL, { params });
  }

  generatePdf(id: string): Observable<Blob> {
    return this.http.get(`${this.API_URL}/${id}/pdf`, { 
      responseType: 'blob' 
    });
  }

  // Méthodes spécifiques pour les utilisateurs
  getMyDocuments(): Observable<Document[]> {
    return this.http.get<Document[]>(`${this.API_URL}/my-documents`);
  }

  updateDocumentStatus(id: string, statut: number): Observable<Document> {
    return this.http.patch<Document>(`${this.API_URL}/${id}/status`, { statut });
  }

  // Types de documents
  getTypesDocuments(): Observable<TypeDocument[]> {
    return this.http.get<TypeDocument[]>(`${this.API_URL}/types`);
  }

  getTypesDocument(): Observable<TypeDocument[]> {
    return this.http.get<TypeDocument[]>(this.TYPE_DOCUMENT_URL);
  }

  getTypeDocument(id: string): Observable<TypeDocument> {
    return this.http.get<TypeDocument>(`${this.TYPE_DOCUMENT_URL}/${id}`);
  }

  createTypeDocument(typeDocument: Partial<TypeDocument>): Observable<TypeDocument> {
    return this.http.post<TypeDocument>(this.TYPE_DOCUMENT_URL, typeDocument);
  }

  updateTypeDocument(id: string, typeDocument: Partial<TypeDocument>): Observable<TypeDocument> {
    return this.http.put<TypeDocument>(`${this.TYPE_DOCUMENT_URL}/${id}`, typeDocument);
  }

  deleteTypeDocument(id: string): Observable<void> {
    return this.http.delete<void>(`${this.TYPE_DOCUMENT_URL}/${id}`);
  }
}
