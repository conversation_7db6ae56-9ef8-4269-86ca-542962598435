import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FactureVente } from 'src/app/models';

@Injectable({
  providedIn: 'root'
})
export class FactureVenteService {
  private readonly API_URL = 'http://localhost:5251/api/factures-vente';

  constructor(private http: HttpClient) {}

  getFacturesVente(): Observable<FactureVente[]> {
    return this.http.get<FactureVente[]>(this.API_URL);
  }

  getFactureVente(id: string): Observable<FactureVente> {
    return this.http.get<FactureVente>(`${this.API_URL}/${id}`);
  }

  createFactureVente(facture: Partial<FactureVente>): Observable<FactureVente> {
    return this.http.post<FactureVente>(this.API_URL, facture);
  }

  updateFactureVente(id: string, facture: Partial<FactureVente>): Observable<FactureVente> {
    return this.http.put<FactureVente>(`${this.API_URL}/${id}`, facture);
  }

  deleteFactureVente(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  getFacturesByClient(clientId: string): Observable<FactureVente[]> {
    const params = new HttpParams().set('clientId', clientId);
    return this.http.get<FactureVente[]>(this.API_URL, { params });
  }

  getFacturesByStatut(statut: number): Observable<FactureVente[]> {
    const params = new HttpParams().set('statut', statut.toString());
    return this.http.get<FactureVente[]>(this.API_URL, { params });
  }

  searchFactures(searchTerm: string): Observable<FactureVente[]> {
    const params = new HttpParams().set('search', searchTerm);
    return this.http.get<FactureVente[]>(`${this.API_URL}/search`, { params });
  }

  generatePdf(id: string): Observable<Blob> {
    return this.http.get(`${this.API_URL}/${id}/pdf`, { 
      responseType: 'blob' 
    });
  }

  sendByEmail(id: string, email: string): Observable<any> {
    return this.http.post(`${this.API_URL}/${id}/send-email`, { email });
  }

  markAsPaid(id: string, datePaiement: Date): Observable<FactureVente> {
    return this.http.patch<FactureVente>(`${this.API_URL}/${id}/mark-paid`, {
      datePaiement: datePaiement.toISOString()
    });
  }

  markAsSent(id: string): Observable<FactureVente> {
    return this.http.patch<FactureVente>(`${this.API_URL}/${id}/mark-sent`, {});
  }
}
