import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ClientService } from '../../services/client.service';
import { AuthService } from '../../services/auth.service';
import { Client, StatutClient } from '../models';

@Component({
  selector: 'app-clients',
  templateUrl: './clients.component.html',
  styleUrls: ['./clients.component.css']
})
export class ClientsComponent implements OnInit {
  clients: Client[] = [];
  filteredClients: Client[] = [];
  loading = false;
  searchTerm = '';
  selectedStatut: StatutClient | null = null;

  // Formulaire pour ajouter/modifier un client
  clientForm!: FormGroup;
  isEditing = false;
  editingClientId: string | null = null;
  showForm = false;

  // Énumérations pour le template
  StatutClient = StatutClient;
  statutOptions = [
    { value: StatutClient.Actif, label: 'Actif' },
    { value: StatutClient.Inactif, label: 'Inactif' },
    { value: StatutClient.Suspendu, label: 'Suspendu' }
  ];

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['nom', 'email', 'telephone', 'statut', 'dateCreation', 'actions'];

  constructor(
    private clientService: ClientService,
    public authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions
    if (!this.authService.canManageClients()) {
      this.snackBar.open('Accès non autorisé', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeForm();
    this.loadClients();
  }

  initializeForm(): void {
    this.clientForm = this.formBuilder.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      telephone: [''],
      adresse: ['', [Validators.required]],
      statut: [StatutClient.Actif, [Validators.required]]
    });
  }

  loadClients(): void {
    this.loading = true;
    this.clientService.getClients().subscribe({
      next: (clients) => {
        this.clients = clients;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des clients');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredClients = this.clients.filter(client => {
      const matchesSearch = !this.searchTerm ||
        client.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesStatut = this.selectedStatut === null || client.statut === this.selectedStatut;

      return matchesSearch && matchesStatut;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatutFilterChange(): void {
    this.applyFilters();
  }

  showAddForm(): void {
    this.isEditing = false;
    this.editingClientId = null;
    this.clientForm.reset();
    this.clientForm.patchValue({ statut: StatutClient.Actif });
    this.showForm = true;
  }

  editClient(client: Client): void {
    this.isEditing = true;
    this.editingClientId = client.id;
    this.clientForm.patchValue({
      nom: client.nom,
      email: client.email,
      telephone: client.telephone,
      adresse: client.adresse,
      statut: client.statut
    });
    this.showForm = true;
  }

  cancelForm(): void {
    this.showForm = false;
    this.isEditing = false;
    this.editingClientId = null;
    this.clientForm.reset();
  }

  onSubmit(): void {
    if (this.clientForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const clientData = this.clientForm.value;

    if (this.isEditing && this.editingClientId) {
      this.updateClient(this.editingClientId, clientData);
    } else {
      this.createClient(clientData);
    }
  }

  createClient(clientData: any): void {
    this.loading = true;
    this.clientService.createClient(clientData).subscribe({
      next: (client) => {
        this.clients.push(client);
        this.applyFilters();
        this.showSuccess('Client créé avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la création du client');
        this.loading = false;
      }
    });
  }

  updateClient(id: string, clientData: any): void {
    this.loading = true;
    this.clientService.updateClient(id, clientData).subscribe({
      next: (updatedClient) => {
        const index = this.clients.findIndex(c => c.id === id);
        if (index !== -1) {
          this.clients[index] = updatedClient;
          this.applyFilters();
        }
        this.showSuccess('Client modifié avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la modification du client');
        this.loading = false;
      }
    });
  }

  deleteClient(client: Client): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le client "${client.nom}" ?`)) {
      this.loading = true;
      this.clientService.deleteClient(client.id).subscribe({
        next: () => {
          this.clients = this.clients.filter(c => c.id !== client.id);
          this.applyFilters();
          this.showSuccess('Client supprimé avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression du client');
          this.loading = false;
        }
      });
    }
  }

  getStatutLabel(statut: StatutClient): string {
    const option = this.statutOptions.find(opt => opt.value === statut);
    return option ? option.label : 'Inconnu';
  }

  getStatutClass(statut: StatutClient): string {
    switch (statut) {
      case StatutClient.Actif:
        return 'statut-actif';
      case StatutClient.Inactif:
        return 'statut-inactif';
      case StatutClient.Suspendu:
        return 'statut-suspendu';
      default:
        return '';
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.clientForm.controls).forEach(key => {
      const control = this.clientForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.clientForm.get('nom'); }
  get email() { return this.clientForm.get('email'); }
  get telephone() { return this.clientForm.get('telephone'); }
  get adresse() { return this.clientForm.get('adresse'); }
  get statut() { return this.clientForm.get('statut'); }
}
