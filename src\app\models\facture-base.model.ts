import { StatutFacture } from './enums';
import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

export interface FactureBase {
  id: string;
  numero: string;
  montant: number;
  date: Date;
  dateEcheance?: Date;
  statut: StatutFacture;
  dateEnvoi?: Date;
  datePaiement?: Date;
  notesInternes?: string;
  matriculeFiscaleSociete: string;
  societeId: string;
  utilisateurCreationId?: string;
  dateCreation: Date;
  dateModification?: Date;
  societe?: Societe;
  utilisateurCreation?: Applicationuser;
}
