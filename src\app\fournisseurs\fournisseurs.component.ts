import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FournisseurService } from '../../services/fournisseur.service';
import { AuthService } from '../../services/auth.service';
import { Fournisseur, StatutFournisseur } from '../models';

@Component({
  selector: 'app-fournisseurs',
  templateUrl: './fournisseurs.component.html',
  styleUrls: ['./fournisseurs.component.css']
})
export class FournisseursComponent implements OnInit {
  fournisseurs: Fournisseur[] = [];
  filteredFournisseurs: Fournisseur[] = [];
  loading = false;
  searchTerm = '';
  selectedStatut: StatutFournisseur | null = null;

  // Formulaire pour ajouter/modifier un fournisseur
  fournisseurForm!: FormGroup;
  isEditing = false;
  editingFournisseurId: string | null = null;
  showForm = false;

  // Énumérations pour le template
  StatutFournisseur = StatutFournisseur;
  statutOptions = [
    { value: StatutFournisseur.Actif, label: 'Actif' },
    { value: StatutFournisseur.Inactif, label: 'Inactif' },
    { value: StatutFournisseur.Suspendu, label: 'Suspendu' }
  ];

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['nom', 'email', 'telephone', 'statut', 'dateCreation', 'actions'];

  constructor(
    private fournisseurService: FournisseurService,
    public authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions
    if (!this.authService.canManageSuppliers()) {
      this.snackBar.open('Accès non autorisé', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeForm();
    this.loadFournisseurs();
  }

  initializeForm(): void {
    this.fournisseurForm = this.formBuilder.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      telephone: [''],
      adresse: ['', [Validators.required]],
      statut: [StatutFournisseur.Actif, [Validators.required]]
    });
  }

  loadFournisseurs(): void {
    this.loading = true;
    this.fournisseurService.getFournisseurs().subscribe({
      next: (fournisseurs) => {
        this.fournisseurs = fournisseurs;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des fournisseurs');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredFournisseurs = this.fournisseurs.filter(fournisseur => {
      const matchesSearch = !this.searchTerm ||
        fournisseur.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        fournisseur.email.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesStatut = this.selectedStatut === null || fournisseur.statut === this.selectedStatut;

      return matchesSearch && matchesStatut;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatutFilterChange(): void {
    this.applyFilters();
  }

  showAddForm(): void {
    this.isEditing = false;
    this.editingFournisseurId = null;
    this.fournisseurForm.reset();
    this.fournisseurForm.patchValue({ statut: StatutFournisseur.Actif });
    this.showForm = true;
  }

  editFournisseur(fournisseur: Fournisseur): void {
    this.isEditing = true;
    this.editingFournisseurId = fournisseur.id;
    this.fournisseurForm.patchValue({
      nom: fournisseur.nom,
      email: fournisseur.email,
      telephone: fournisseur.telephone,
      adresse: fournisseur.adresse,
      statut: fournisseur.statut
    });
    this.showForm = true;
  }

  cancelForm(): void {
    this.showForm = false;
    this.isEditing = false;
    this.editingFournisseurId = null;
    this.fournisseurForm.reset();
  }

  onSubmit(): void {
    if (this.fournisseurForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const fournisseurData = this.fournisseurForm.value;

    if (this.isEditing && this.editingFournisseurId) {
      this.updateFournisseur(this.editingFournisseurId, fournisseurData);
    } else {
      this.createFournisseur(fournisseurData);
    }
  }

  createFournisseur(fournisseurData: any): void {
    this.loading = true;
    this.fournisseurService.createFournisseur(fournisseurData).subscribe({
      next: (fournisseur) => {
        this.fournisseurs.push(fournisseur);
        this.applyFilters();
        this.showSuccess('Fournisseur créé avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la création du fournisseur');
        this.loading = false;
      }
    });
  }

  updateFournisseur(id: string, fournisseurData: any): void {
    this.loading = true;
    this.fournisseurService.updateFournisseur(id, fournisseurData).subscribe({
      next: (updatedFournisseur) => {
        const index = this.fournisseurs.findIndex(f => f.id === id);
        if (index !== -1) {
          this.fournisseurs[index] = updatedFournisseur;
          this.applyFilters();
        }
        this.showSuccess('Fournisseur modifié avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la modification du fournisseur');
        this.loading = false;
      }
    });
  }

  deleteFournisseur(fournisseur: Fournisseur): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le fournisseur "${fournisseur.nom}" ?`)) {
      this.loading = true;
      this.fournisseurService.deleteFournisseur(fournisseur.id).subscribe({
        next: () => {
          this.fournisseurs = this.fournisseurs.filter(f => f.id !== fournisseur.id);
          this.applyFilters();
          this.showSuccess('Fournisseur supprimé avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression du fournisseur');
          this.loading = false;
        }
      });
    }
  }

  getStatutLabel(statut: StatutFournisseur): string {
    const option = this.statutOptions.find(opt => opt.value === statut);
    return option ? option.label : 'Inconnu';
  }

  getStatutClass(statut: StatutFournisseur): string {
    switch (statut) {
      case StatutFournisseur.Actif:
        return 'statut-actif';
      case StatutFournisseur.Inactif:
        return 'statut-inactif';
      case StatutFournisseur.Suspendu:
        return 'statut-suspendu';
      default:
        return '';
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.fournisseurForm.controls).forEach(key => {
      const control = this.fournisseurForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.fournisseurForm.get('nom'); }
  get email() { return this.fournisseurForm.get('email'); }
  get telephone() { return this.fournisseurForm.get('telephone'); }
  get adresse() { return this.fournisseurForm.get('adresse'); }
  get statut() { return this.fournisseurForm.get('statut'); }
}
