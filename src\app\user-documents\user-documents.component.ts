import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DocumentService } from '../../services/document.service';
import { AuthService } from '../../services/auth.service';
import { Document, TypeDocument, StatutDocument, TypeDemandeDocument } from '../models';

@Component({
  selector: 'app-user-documents',
  templateUrl: './user-documents.component.html',
  styleUrls: ['./user-documents.component.css']
})
export class UserDocumentsComponent implements OnInit {
  documents: Document[] = [];
  filteredDocuments: Document[] = [];
  typesDocuments: TypeDocument[] = [];
  loading = false;
  searchTerm = '';
  selectedStatut: StatutDocument | null = null;

  // Formulaire pour demander un document
  documentForm!: FormGroup;
  showForm = false;

  // Énumérations pour le template
  StatutDocument = StatutDocument;
  TypeDemandeDocument = TypeDemandeDocument;

  statutOptions = [
    { value: StatutDocument.EnAttente, label: 'En attente' },
    { value: StatutDocument.Approuve, label: 'Approuvé' },
    { value: StatutDocument.Rejete, label: 'Rejeté' }
  ];

  typeDemandeOptions = [
    { value: TypeDemandeDocument.AttestationTravail, label: 'Attestation de travail' },
    { value: TypeDemandeDocument.AttestationStage, label: 'Attestation de stage' },
    { value: TypeDemandeDocument.CertificatTravail, label: 'Certificat de travail' },
    { value: TypeDemandeDocument.Autre, label: 'Autre' }
  ];

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['titre', 'typeDemande', 'typeDocument', 'dateCreation', 'statut', 'actions'];

  constructor(
    private documentService: DocumentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadTypesDocuments();
    this.loadMyDocuments();
  }

  initializeForm(): void {
    this.documentForm = this.formBuilder.group({
      titre: ['', [Validators.required, Validators.minLength(2)]],
      contenu: ['', [Validators.required]],
      typeDemande: [TypeDemandeDocument.AttestationTravail, [Validators.required]],
      typeDocumentId: ['', [Validators.required]]
    });
  }

  loadTypesDocuments(): void {
    this.documentService.getTypesDocuments().subscribe({
      next: (types) => {
        this.typesDocuments = types;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des types de documents');
      }
    });
  }

  loadMyDocuments(): void {
    this.loading = true;
    // Charger seulement les documents de l'utilisateur connecté
    this.documentService.getMyDocuments().subscribe({
      next: (documents) => {
        this.documents = documents;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement de vos documents');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredDocuments = this.documents.filter(document => {
      const matchesSearch = !this.searchTerm ||
        document.titre.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesStatut = this.selectedStatut === null || document.statut === this.selectedStatut;

      return matchesSearch && matchesStatut;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatutFilterChange(): void {
    this.applyFilters();
  }

  showDocumentForm(): void {
    this.documentForm.reset();
    this.documentForm.patchValue({
      typeDemande: TypeDemandeDocument.AttestationTravail
    });
    this.showForm = true;
  }

  cancelForm(): void {
    this.showForm = false;
    this.documentForm.reset();
  }

  onSubmit(): void {
    if (this.documentForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const documentData = this.documentForm.value;
    this.loading = true;

    this.documentService.createDocument(documentData).subscribe({
      next: (document) => {
        this.documents.unshift(document); // Ajouter en début de liste
        this.applyFilters();
        this.showSuccess('Demande de document créée avec succès');
        this.cancelForm();
        this.loading = false;

        // Notification automatique aux admins (géré côté backend)
      },
      error: (error) => {
        this.showError('Erreur lors de la création de la demande');
        this.loading = false;
      }
    });
  }

  deleteDocument(document: Document): void {
    // Seuls les documents en attente peuvent être supprimés par l'utilisateur
    if (document.statut !== StatutDocument.EnAttente) {
      this.showError('Vous ne pouvez supprimer que les documents en attente');
      return;
    }

    if (confirm(`Êtes-vous sûr de vouloir supprimer la demande "${document.titre}" ?`)) {
      this.loading = true;
      this.documentService.deleteDocument(document.id).subscribe({
        next: () => {
          this.documents = this.documents.filter(d => d.id !== document.id);
          this.applyFilters();
          this.showSuccess('Demande supprimée avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression de la demande');
          this.loading = false;
        }
      });
    }
  }

  getStatutLabel(statut: StatutDocument): string {
    const option = this.statutOptions.find(opt => opt.value === statut);
    return option ? option.label : 'Inconnu';
  }

  getStatutClass(statut: StatutDocument): string {
    switch (statut) {
      case StatutDocument.EnAttente:
        return 'statut-attente';
      case StatutDocument.Approuve:
        return 'statut-approuve';
      case StatutDocument.Rejete:
        return 'statut-rejete';
      default:
        return '';
    }
  }

  getTypeDemandeLabel(typeDemande: TypeDemandeDocument): string {
    const option = this.typeDemandeOptions.find(opt => opt.value === typeDemande);
    return option ? option.label : 'Inconnu';
  }

  getTypeDocumentName(typeDocumentId: string): string {
    const type = this.typesDocuments.find(t => t.id === typeDocumentId);
    return type ? type.nom : 'Type inconnu';
  }

  canDelete(document: Document): boolean {
    return document.statut === StatutDocument.EnAttente;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.documentForm.controls).forEach(key => {
      const control = this.documentForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get titre() { return this.documentForm.get('titre'); }
  get contenu() { return this.documentForm.get('contenu'); }
  get typeDemande() { return this.documentForm.get('typeDemande'); }
  get typeDocumentId() { return this.documentForm.get('typeDocumentId'); }
}
