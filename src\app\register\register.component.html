<div class="register-container">
  <mat-card class="register-card">
    <mat-card-header>
      <mat-card-title>Inscription</mat-card-title>
      <mat-card-subtitle>Créez votre compte et votre société</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">

        <!-- Section Informations Utilisateur -->
        <h3 class="section-title">
          <mat-icon>person</mat-icon>
          Informations Utilisateur
        </h3>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Nom complet</mat-label>
            <input matInput formControlName="nom" placeholder="Votre nom complet">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="nom?.hasError('required')">Le nom est requis</mat-error>
            <mat-error *ngIf="nom?.hasError('minlength')">Le nom doit contenir au moins 2 caractères</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Nom d'utilisateur</mat-label>
            <input matInput formControlName="userName" placeholder="Nom d'utilisateur">
            <mat-icon matSuffix>account_circle</mat-icon>
            <mat-error *ngIf="userName?.hasError('required')">Le nom d'utilisateur est requis</mat-error>
            <mat-error *ngIf="userName?.hasError('minlength')">Le nom d'utilisateur doit contenir au moins 3 caractères</mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="email?.hasError('required')">L'email est requis</mat-error>
          <mat-error *ngIf="email?.hasError('email')">Format d'email invalide</mat-error>
        </mat-form-field>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Mot de passe</mat-label>
            <input matInput [type]="hidePassword ? 'password' : 'text'"
                   formControlName="motDePasse" placeholder="Mot de passe">
            <button mat-icon-button matSuffix (click)="togglePasswordVisibility()" type="button">
              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="motDePasse?.hasError('required')">Le mot de passe est requis</mat-error>
            <mat-error *ngIf="motDePasse?.hasError('minlength')">Le mot de passe doit contenir au moins 6 caractères</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Confirmer le mot de passe</mat-label>
            <input matInput [type]="hideConfirmPassword ? 'password' : 'text'"
                   formControlName="confirmMotDePasse" placeholder="Confirmer le mot de passe">
            <button mat-icon-button matSuffix (click)="toggleConfirmPasswordVisibility()" type="button">
              <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="confirmMotDePasse?.hasError('required')">La confirmation est requise</mat-error>
            <mat-error *ngIf="confirmMotDePasse?.hasError('passwordMismatch')">Les mots de passe ne correspondent pas</mat-error>
          </mat-form-field>
        </div>

        <!-- Section Informations Société -->
        <h3 class="section-title">
          <mat-icon>business</mat-icon>
          Informations Société
        </h3>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nom de la société</mat-label>
          <input matInput formControlName="nomSociete" placeholder="Nom de votre société">
          <mat-icon matSuffix>business</mat-icon>
          <mat-error *ngIf="nomSociete?.hasError('required')">Le nom de la société est requis</mat-error>
          <mat-error *ngIf="nomSociete?.hasError('minlength')">Le nom doit contenir au moins 2 caractères</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Adresse de la société</mat-label>
          <textarea matInput formControlName="adresseSociete" placeholder="Adresse complète de la société" rows="3"></textarea>
          <mat-icon matSuffix>location_on</mat-icon>
          <mat-error *ngIf="adresseSociete?.hasError('required')">L'adresse est requise</mat-error>
        </mat-form-field>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Matricule fiscal</mat-label>
            <input matInput formControlName="matriculeFiscale" placeholder="Matricule fiscal unique">
            <mat-icon matSuffix>receipt</mat-icon>
            <mat-error *ngIf="matriculeFiscale?.hasError('required')">Le matricule fiscal est requis</mat-error>
            <mat-error *ngIf="matriculeFiscale?.hasError('minlength')">Le matricule doit contenir au moins 8 caractères</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Téléphone (optionnel)</mat-label>
            <input matInput formControlName="telephoneSociete" placeholder="Téléphone de la société">
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email société (optionnel)</mat-label>
          <input matInput type="email" formControlName="emailSociete" placeholder="<EMAIL>">
          <mat-icon matSuffix>business_center</mat-icon>
          <mat-error *ngIf="emailSociete?.hasError('email')">Format d'email invalide</mat-error>
        </mat-form-field>

        <!-- Message d'erreur -->
        <div *ngIf="error" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ error }}</span>
        </div>

        <!-- Bouton d'inscription -->
        <button mat-raised-button color="primary" type="submit"
                class="full-width register-button" [disabled]="loading">
          <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
          <span *ngIf="!loading">Créer le compte</span>
          <span *ngIf="loading">Création en cours...</span>
        </button>
      </form>
    </mat-card-content>

    <mat-card-actions align="center">
      <p>Déjà un compte ?
        <a routerLink="/login" class="login-link">Se connecter</a>
      </p>
    </mat-card-actions>
  </mat-card>
</div>
