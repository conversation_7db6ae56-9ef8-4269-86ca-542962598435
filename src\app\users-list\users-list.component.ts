import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from '../../services/user.service';
import { AuthService } from '../../services/auth.service';


@Component({
  selector: 'app-users-list',
  templateUrl: './users-list.component.html',
  styleUrls: ['./users-list.component.css']
})
export class UsersListComponent implements OnInit {
  users: any[] = []; // Utilisation de any car le service retourne des DTOs
  filteredUsers: any[] = [];
  loading = false;
  searchTerm = '';
  selectedRole: string | null = null;

  // Formulaire pour ajouter/modifier un utilisateur
  userForm!: FormGroup;
  isEditing = false;
  editingUserId: string | null = null;
  showForm = false;

  // Options de rôles
  roleOptions = [
    { value: 'Admin', label: 'Administrateur' },
    { value: 'User', label: 'Utilisateur' }
  ];

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['nom', 'email', 'userName', 'role', 'dateCreation', 'actif', 'actions'];

  constructor(
    private userService: UserService,
    public authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions - seuls les admins peuvent gérer les utilisateurs
    if (!this.authService.canManageUsers()) {
      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeForm();
    this.loadUsers();
  }

  initializeForm(): void {
    this.userForm = this.formBuilder.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      userName: ['', [Validators.required, Validators.minLength(3)]],
      motDePasse: ['', [Validators.required, Validators.minLength(6)]],
      role: ['User', [Validators.required]],
      actif: [true]
    });
  }

  loadUsers(): void {
    this.loading = true;
    this.userService.getUtilisateurs().subscribe({
      next: (users) => {
        this.users = users;
        this.applyFilters();
        this.loading = false;
      },
      error: (error: any) => {
        this.showError('Erreur lors du chargement des utilisateurs');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredUsers = this.users.filter(user => {
      const matchesSearch = !this.searchTerm ||
        user.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        user.userName.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesRole = this.selectedRole === null || user.role === this.selectedRole;

      return matchesSearch && matchesRole;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onRoleFilterChange(): void {
    this.applyFilters();
  }

  showAddForm(): void {
    this.isEditing = false;
    this.editingUserId = null;
    this.userForm.reset();
    this.userForm.patchValue({
      role: 'User',
      actif: true
    });
    // Le mot de passe est requis pour la création
    this.userForm.get('motDePasse')?.setValidators([Validators.required, Validators.minLength(6)]);
    this.userForm.get('motDePasse')?.updateValueAndValidity();
    this.showForm = true;
  }

  editUser(user: any): void {
    this.isEditing = true;
    this.editingUserId = user.id;
    this.userForm.patchValue({
      nom: user.nom,
      email: user.email,
      userName: user.userName,
      role: user.role,
      actif: user.actif
    });
    // Le mot de passe n'est pas requis pour la modification
    this.userForm.get('motDePasse')?.clearValidators();
    this.userForm.get('motDePasse')?.updateValueAndValidity();
    this.showForm = true;
  }

  cancelForm(): void {
    this.showForm = false;
    this.isEditing = false;
    this.editingUserId = null;
    this.userForm.reset();
  }

  onSubmit(): void {
    if (this.userForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const userData = this.userForm.value;

    // Ne pas envoyer le mot de passe vide lors de la modification
    if (this.isEditing && !userData.motDePasse) {
      delete userData.motDePasse;
    }

    if (this.isEditing && this.editingUserId) {
      this.updateUser(this.editingUserId, userData);
    } else {
      this.createUser(userData);
    }
  }

  createUser(userData: any): void {
    this.loading = true;
    this.userService.createUtilisateur(userData).subscribe({
      next: (user) => {
        this.users.push(user);
        this.applyFilters();
        this.showSuccess('Utilisateur créé avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la création de l\'utilisateur');
        this.loading = false;
      }
    });
  }

  updateUser(id: string, userData: any): void {
    this.loading = true;
    this.userService.updateUtilisateur(id, userData).subscribe({
      next: () => {
        // Recharger la liste après modification
        this.loadUsers();
        this.showSuccess('Utilisateur modifié avec succès');
        this.cancelForm();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors de la modification de l\'utilisateur');
        this.loading = false;
      }
    });
  }

  deleteUser(user: any): void {
    // Empêcher la suppression de son propre compte
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.id === user.id) {
      this.showError('Vous ne pouvez pas supprimer votre propre compte');
      return;
    }

    if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${user.nom}" ?`)) {
      this.loading = true;
      this.userService.deleteUtilisateur(user.id).subscribe({
        next: () => {
          this.users = this.users.filter(u => u.id !== user.id);
          this.applyFilters();
          this.showSuccess('Utilisateur supprimé avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression de l\'utilisateur');
          this.loading = false;
        }
      });
    }
  }

  toggleUserStatus(user: any): void {
    const newStatus = !user.actif;
    const action = newStatus ? 'activer' : 'désactiver';

    if (confirm(`Êtes-vous sûr de vouloir ${action} l'utilisateur "${user.nom}" ?`)) {
      this.loading = true;
      this.userService.updateUtilisateur(user.id, { actif: newStatus }).subscribe({
        next: () => {
          user.actif = newStatus;
          this.showSuccess(`Utilisateur ${newStatus ? 'activé' : 'désactivé'} avec succès`);
          this.loading = false;
        },
        error: (error) => {
          this.showError(`Erreur lors de la modification du statut`);
          this.loading = false;
        }
      });
    }
  }

  getRoleLabel(role: string): string {
    const option = this.roleOptions.find(opt => opt.value === role);
    return option ? option.label : role;
  }

  getRoleClass(role: string): string {
    switch (role) {
      case 'Admin':
        return 'role-admin';
      case 'User':
        return 'role-user';
      default:
        return '';
    }
  }

  canDeleteUser(user: any): boolean {
    const currentUser = this.authService.getCurrentUser();
    return !!(currentUser && currentUser.id !== user.id);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get nom() { return this.userForm.get('nom'); }
  get email() { return this.userForm.get('email'); }
  get userName() { return this.userForm.get('userName'); }
  get motDePasse() { return this.userForm.get('motDePasse'); }
  get role() { return this.userForm.get('role'); }
  get actif() { return this.userForm.get('actif'); }
}
