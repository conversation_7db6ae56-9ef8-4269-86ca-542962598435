<div class="clients-container">
  <!-- En-tête -->
  <div class="header">
    <h1>
      <mat-icon>people</mat-icon>
      Gestion des Clients
    </h1>
    <button mat-raised-button color="primary" (click)="showAddForm()"
            *ngIf="authService.canManageClients()">
      <mat-icon>add</mat-icon>
      Nouveau Client
    </button>
  </div>

  <!-- Filtres et recherche -->
  <mat-card class="filters-card">
    <div class="filters-row">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Rechercher</mat-label>
        <input matInput [(ngModel)]="searchTerm" (input)="onSearchChange()"
               placeholder="Nom ou email du client">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>Filtrer par statut</mat-label>
        <mat-select [(value)]="selectedStatut" (selectionChange)="onStatutFilterChange()">
          <mat-option [value]="null">Tous les statuts</mat-option>
          <mat-option *ngFor="let option of statutOptions" [value]="option.value">
            {{ option.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <button mat-icon-button (click)="loadClients()" matTooltip="Actualiser">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </mat-card>

  <!-- Formulaire d'ajout/modification -->
  <mat-card *ngIf="showForm" class="form-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>{{ isEditing ? 'edit' : 'add' }}</mat-icon>
        {{ isEditing ? 'Modifier le client' : 'Nouveau client' }}
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="clientForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Nom complet</mat-label>
            <input matInput formControlName="nom" placeholder="Nom du client">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="nom?.hasError('required')">Le nom est requis</mat-error>
            <mat-error *ngIf="nom?.hasError('minlength')">Le nom doit contenir au moins 2 caractères</mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Email</mat-label>
            <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
            <mat-icon matSuffix>email</mat-icon>
            <mat-error *ngIf="email?.hasError('required')">L'email est requis</mat-error>
            <mat-error *ngIf="email?.hasError('email')">Format d'email invalide</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Téléphone</mat-label>
            <input matInput formControlName="telephone" placeholder="Numéro de téléphone">
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Adresse</mat-label>
            <textarea matInput formControlName="adresse" placeholder="Adresse complète" rows="3"></textarea>
            <mat-icon matSuffix>location_on</mat-icon>
            <mat-error *ngIf="adresse?.hasError('required')">L'adresse est requise</mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Statut</mat-label>
            <mat-select formControlName="statut">
              <mat-option *ngFor="let option of statutOptions" [value]="option.value">
                {{ option.label }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="statut?.hasError('required')">Le statut est requis</mat-error>
          </mat-form-field>
        </div>

        <div class="form-actions">
          <button mat-button type="button" (click)="cancelForm()">
            Annuler
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="loading">
            <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
            <span *ngIf="!loading">{{ isEditing ? 'Modifier' : 'Créer' }}</span>
            <span *ngIf="loading">{{ isEditing ? 'Modification...' : 'Création...' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Liste des clients -->
  <mat-card class="table-card">
    <mat-card-header>
      <mat-card-title>
        Liste des clients ({{ filteredClients.length }})
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Chargement des clients...</p>
      </div>

      <div *ngIf="!loading && filteredClients.length === 0" class="no-data">
        <mat-icon>people_outline</mat-icon>
        <p>Aucun client trouvé</p>
        <button mat-raised-button color="primary" (click)="showAddForm()"
                *ngIf="authService.canManageClients()">
          <mat-icon>add</mat-icon>
          Ajouter le premier client
        </button>
      </div>

      <table mat-table [dataSource]="filteredClients" *ngIf="!loading && filteredClients.length > 0"
             class="clients-table">

        <!-- Colonne Nom -->
        <ng-container matColumnDef="nom">
          <th mat-header-cell *matHeaderCellDef>Nom</th>
          <td mat-cell *matCellDef="let client">
            <div class="client-info">
              <mat-icon class="client-icon">person</mat-icon>
              <span class="client-name">{{ client.nom }}</span>
            </div>
          </td>
        </ng-container>

        <!-- Colonne Email -->
        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef>Email</th>
          <td mat-cell *matCellDef="let client">
            <a href="mailto:{{ client.email }}" class="email-link">{{ client.email }}</a>
          </td>
        </ng-container>

        <!-- Colonne Téléphone -->
        <ng-container matColumnDef="telephone">
          <th mat-header-cell *matHeaderCellDef>Téléphone</th>
          <td mat-cell *matCellDef="let client">
            <span *ngIf="client.telephone; else noPhone">{{ client.telephone }}</span>
            <ng-template #noPhone>
              <span class="no-data-text">Non renseigné</span>
            </ng-template>
          </td>
        </ng-container>

        <!-- Colonne Statut -->
        <ng-container matColumnDef="statut">
          <th mat-header-cell *matHeaderCellDef>Statut</th>
          <td mat-cell *matCellDef="let client">
            <span class="statut-badge" [ngClass]="getStatutClass(client.statut)">
              {{ getStatutLabel(client.statut) }}
            </span>
          </td>
        </ng-container>

        <!-- Colonne Date de création -->
        <ng-container matColumnDef="dateCreation">
          <th mat-header-cell *matHeaderCellDef>Date de création</th>
          <td mat-cell *matCellDef="let client">
            {{ client.dateCreation | date:'dd/MM/yyyy' }}
          </td>
        </ng-container>

        <!-- Colonne Actions -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let client">
            <button mat-icon-button (click)="editClient(client)"
                    matTooltip="Modifier" *ngIf="authService.canManageClients()">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteClient(client)"
                    matTooltip="Supprimer" color="warn" *ngIf="authService.canManageClients()">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </mat-card-content>
  </mat-card>
</div>
