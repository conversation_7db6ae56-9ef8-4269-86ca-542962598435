import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FactureAchatService } from '../../services/facture-achat.service';
import { FournisseurService } from '../../services/fournisseur.service';
import { AuthService } from '../../services/auth.service';
import { FactureAchat, StatutFacture, Fournisseur } from '../models';

@Component({
  selector: 'app-factures-achat',
  templateUrl: './factures-achat.component.html',
  styleUrls: ['./factures-achat.component.css']
})
export class FacturesAchatComponent implements OnInit {
  factures: FactureAchat[] = [];
  filteredFactures: FactureAchat[] = [];
  fournisseurs: Fournisseur[] = [];
  loading = false;
  searchTerm = '';
  selectedStatut: StatutFacture | null = null;
  selectedFournisseur: string | null = null;

  // Formulaire pour ajouter/modifier une facture
  factureForm!: FormGroup;
  isEditing = false;
  editingFactureId: string | null = null;
  showForm = false;

  // Gestion des fichiers
  selectedFile: File | null = null;
  filePreview: string | null = null;
  dragOver = false;

  // Énumérations pour le template
  StatutFacture = StatutFacture;
  statutOptions = [
    { value: StatutFacture.Brouillon, label: 'Brouillon' },
    { value: StatutFacture.Envoyee, label: 'Envoyée' },
    { value: StatutFacture.Payee, label: 'Payée' },
    { value: StatutFacture.EnRetard, label: 'En retard' },
    { value: StatutFacture.Annulee, label: 'Annulée' }
  ];

  // Types de fichiers acceptés
  acceptedFileTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.gif'];
  maxFileSize = 10 * 1024 * 1024; // 10MB

  // Colonnes à afficher dans le tableau
  displayedColumns: string[] = ['numero', 'fournisseur', 'montant', 'date', 'dateEcheance', 'statut', 'fichier', 'actions'];

  constructor(
    private factureAchatService: FactureAchatService,
    private fournisseurService: FournisseurService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Vérifier les permissions
    if (!this.authService.canCreateInvoices()) {
      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });
      return;
    }

    this.initializeForm();
    this.loadFournisseurs();
    this.loadFactures();
  }

  initializeForm(): void {
    this.factureForm = this.formBuilder.group({
      numero: ['', [Validators.required]],
      fournisseurId: ['', [Validators.required]],
      montant: ['', [Validators.required, Validators.min(0.01)]],
      date: [new Date(), [Validators.required]],
      dateEcheance: [''],
      statut: [StatutFacture.Brouillon, [Validators.required]],
      notesInternes: ['']
    });
  }

  loadFournisseurs(): void {
    this.fournisseurService.getFournisseurs().subscribe({
      next: (fournisseurs) => {
        this.fournisseurs = fournisseurs.filter(f => f.statut === 0); // Seulement les fournisseurs actifs
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des fournisseurs');
      }
    });
  }

  loadFactures(): void {
    this.loading = true;
    this.factureAchatService.getFacturesAchat().subscribe({
      next: (factures) => {
        this.factures = factures;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des factures');
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.filteredFactures = this.factures.filter(facture => {
      const matchesSearch = !this.searchTerm ||
        facture.numero.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (facture.fournisseur?.nom.toLowerCase().includes(this.searchTerm.toLowerCase()));

      const matchesStatut = this.selectedStatut === null || facture.statut === this.selectedStatut;
      const matchesFournisseur = this.selectedFournisseur === null || facture.fournisseurId === this.selectedFournisseur;

      return matchesSearch && matchesStatut && matchesFournisseur;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatutFilterChange(): void {
    this.applyFilters();
  }

  onFournisseurFilterChange(): void {
    this.applyFilters();
  }

  showAddForm(): void {
    this.isEditing = false;
    this.editingFactureId = null;
    this.factureForm.reset();
    this.factureForm.patchValue({
      date: new Date(),
      statut: StatutFacture.Brouillon
    });
    this.generateFactureNumber();
    this.resetFileSelection();
    this.showForm = true;
  }

  editFacture(facture: FactureAchat): void {
    this.isEditing = true;
    this.editingFactureId = facture.id;
    this.factureForm.patchValue({
      numero: facture.numero,
      fournisseurId: facture.fournisseurId,
      montant: facture.montant,
      date: new Date(facture.date),
      dateEcheance: facture.dateEcheance ? new Date(facture.dateEcheance) : null,
      statut: facture.statut,
      notesInternes: facture.notesInternes
    });
    this.resetFileSelection();
    this.showForm = true;
  }

  cancelForm(): void {
    this.showForm = false;
    this.isEditing = false;
    this.editingFactureId = null;
    this.factureForm.reset();
    this.resetFileSelection();
  }

  // Gestion des fichiers
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  onFileDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
  }

  handleFileSelection(file: File): void {
    // Vérifier le type de fichier
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!this.acceptedFileTypes.includes(fileExtension)) {
      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedFileTypes.join(', '));
      return;
    }

    // Vérifier la taille du fichier
    if (file.size > this.maxFileSize) {
      this.showError('Fichier trop volumineux. Taille maximale: 10MB');
      return;
    }

    this.selectedFile = file;

    // Créer un aperçu pour les images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.filePreview = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    } else {
      this.filePreview = null;
    }
  }

  removeFile(): void {
    this.resetFileSelection();
  }

  resetFileSelection(): void {
    this.selectedFile = null;
    this.filePreview = null;
    this.dragOver = false;
  }

  onSubmit(): void {
    if (this.factureForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const factureData = this.factureForm.value;

    if (this.isEditing && this.editingFactureId) {
      this.updateFacture(this.editingFactureId, factureData);
    } else {
      this.createFacture(factureData);
    }
  }

  createFacture(factureData: any): void {
    this.loading = true;

    // Si un fichier est sélectionné, l'inclure dans les données
    if (this.selectedFile) {
      const formData = new FormData();
      Object.keys(factureData).forEach(key => {
        formData.append(key, factureData[key]);
      });
      formData.append('fichier', this.selectedFile);

      this.factureAchatService.createFactureAchatWithFile(formData).subscribe({
        next: (facture) => {
          this.factures.push(facture);
          this.applyFilters();
          this.showSuccess('Facture créée avec succès');
          this.cancelForm();
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la création de la facture');
          this.loading = false;
        }
      });
    } else {
      this.factureAchatService.createFactureAchat(factureData).subscribe({
        next: (facture) => {
          this.factures.push(facture);
          this.applyFilters();
          this.showSuccess('Facture créée avec succès');
          this.cancelForm();
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la création de la facture');
          this.loading = false;
        }
      });
    }
  }

  updateFacture(id: string, factureData: any): void {
    this.loading = true;

    // Si un nouveau fichier est sélectionné, l'inclure dans la mise à jour
    if (this.selectedFile) {
      const formData = new FormData();
      Object.keys(factureData).forEach(key => {
        formData.append(key, factureData[key]);
      });
      formData.append('fichier', this.selectedFile);

      this.factureAchatService.updateFactureAchatWithFile(id, formData).subscribe({
        next: (updatedFacture) => {
          const index = this.factures.findIndex(f => f.id === id);
          if (index !== -1) {
            this.factures[index] = updatedFacture;
            this.applyFilters();
          }
          this.showSuccess('Facture modifiée avec succès');
          this.cancelForm();
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la modification de la facture');
          this.loading = false;
        }
      });
    } else {
      this.factureAchatService.updateFactureAchat(id, factureData).subscribe({
        next: (updatedFacture) => {
          const index = this.factures.findIndex(f => f.id === id);
          if (index !== -1) {
            this.factures[index] = updatedFacture;
            this.applyFilters();
          }
          this.showSuccess('Facture modifiée avec succès');
          this.cancelForm();
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la modification de la facture');
          this.loading = false;
        }
      });
    }
  }

  deleteFacture(facture: FactureAchat): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la facture "${facture.numero}" ?`)) {
      this.loading = true;
      this.factureAchatService.deleteFactureAchat(facture.id).subscribe({
        next: () => {
          this.factures = this.factures.filter(f => f.id !== facture.id);
          this.applyFilters();
          this.showSuccess('Facture supprimée avec succès');
          this.loading = false;
        },
        error: (error) => {
          this.showError('Erreur lors de la suppression de la facture');
          this.loading = false;
        }
      });
    }
  }

  downloadFile(facture: FactureAchat): void {
    if (facture.cheminFichier) {
      this.factureAchatService.downloadFile(facture.id).subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = facture.nomFichierOriginal || 'facture.pdf';
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          this.showError('Erreur lors du téléchargement du fichier');
        }
      });
    }
  }

  generateFactureNumber(): void {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');

    const numero = `FA-${year}${month}${day}-${time}`;
    this.factureForm.patchValue({ numero });
  }

  getStatutLabel(statut: StatutFacture): string {
    const option = this.statutOptions.find(opt => opt.value === statut);
    return option ? option.label : 'Inconnu';
  }

  getStatutClass(statut: StatutFacture): string {
    switch (statut) {
      case StatutFacture.Brouillon:
        return 'statut-brouillon';
      case StatutFacture.Envoyee:
        return 'statut-envoyee';
      case StatutFacture.Payee:
        return 'statut-payee';
      case StatutFacture.EnRetard:
        return 'statut-retard';
      case StatutFacture.Annulee:
        return 'statut-annulee';
      default:
        return '';
    }
  }

  getFournisseurName(fournisseurId: string): string {
    const fournisseur = this.fournisseurs.find(f => f.id === fournisseurId);
    return fournisseur ? fournisseur.nom : 'Fournisseur inconnu';
  }

  hasFile(facture: FactureAchat): boolean {
    return !!(facture.cheminFichier && facture.nomFichierOriginal);
  }

  getFileSize(sizeInBytes: number | undefined): string {
    if (!sizeInBytes) return '';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = sizeInBytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.factureForm.controls).forEach(key => {
      const control = this.factureForm.get(key);
      control?.markAsTouched();
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get numero() { return this.factureForm.get('numero'); }
  get fournisseurId() { return this.factureForm.get('fournisseurId'); }
  get montant() { return this.factureForm.get('montant'); }
  get date() { return this.factureForm.get('date'); }
  get dateEcheance() { return this.factureForm.get('dateEcheance'); }
  get statut() { return this.factureForm.get('statut'); }
  get notesInternes() { return this.factureForm.get('notesInternes'); }
}
