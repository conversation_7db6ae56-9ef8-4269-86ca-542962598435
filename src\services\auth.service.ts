import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { Router } from '@angular/router';
import { Applicationuser } from 'src/app/models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly API_URL = '/api/Auth';
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'current_user';

  private currentUserSubject = new BehaviorSubject<Applicationuser | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.loadUserFromStorage();
  }

  login(email: string, motDePasse: string): Observable<any> {
    return this.http.post<any>(`${this.API_URL}/login`, { email, motDePasse })
      .pipe(
        tap(response => {
          if (response.token) {
            this.setSession(response);
          }
        })
      );
  }

  register(userData: any): Observable<any> {
    return this.http.post<any>(`${this.API_URL}/register`, userData)
      .pipe(
        tap(response => {
          if (response.token) {
            this.setSession(response);
          }
        })
      );
  }

  logout(): Observable<any> {
    return this.http.post(`${this.API_URL}/logout`, {})
      .pipe(
        tap(() => {
          this.clearSession();
        })
      );
  }

  private setSession(authResult: any): void {
    localStorage.setItem(this.TOKEN_KEY, authResult.token);
    localStorage.setItem(this.USER_KEY, JSON.stringify(authResult.utilisateur));
    
    this.currentUserSubject.next(authResult.utilisateur);
    this.isAuthenticatedSubject.next(true);
  }

  private clearSession(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    
    this.router.navigate(['/login']);
  }

  private loadUserFromStorage(): void {
    const token = localStorage.getItem(this.TOKEN_KEY);
    const userStr = localStorage.getItem(this.USER_KEY);
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
      } catch (error) {
        this.clearSession();
      }
    }
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getCurrentUser(): Applicationuser | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Vérifier le rôle basé sur une propriété role dans l'utilisateur
    // Assumant que le backend retourne un objet avec une propriété role
    return (user as any).role === role || user.userName === role;
  }

  isAdmin(): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Vérifier si l'utilisateur a le rôle Admin
    return (user as any).role === 'Admin' || (user as any).role === 0; // 0 = Admin selon l'enum
  }

  isUser(): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Vérifier si l'utilisateur a le rôle User
    return (user as any).role === 'User' || (user as any).role === 1; // 1 = User selon l'enum
  }

  // Vérifier les permissions spécifiques
  canManageUsers(): boolean {
    return this.isAdmin();
  }

  canManageCompany(): boolean {
    return this.isAdmin();
  }

  canManageDocuments(): boolean {
    return this.isAdmin();
  }

  canCreateInvoices(): boolean {
    return this.isAdmin(); // Seuls les admins peuvent créer des factures
  }

  canViewAllInvoices(): boolean {
    return this.isAdmin();
  }

  canManageClients(): boolean {
    return this.isAdmin();
  }

  canManageSuppliers(): boolean {
    return this.isAdmin();
  }

  refreshToken(): Observable<any> {
    return this.http.post<any>(`${this.API_URL}/refresh-token`, {})
      .pipe(
        tap(response => {
          if (response.token) {
            localStorage.setItem(this.TOKEN_KEY, response.token);
          }
        })
      );
  }
}
